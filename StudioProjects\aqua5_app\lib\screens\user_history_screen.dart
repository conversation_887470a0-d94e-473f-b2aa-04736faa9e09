import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fl_chart/fl_chart.dart';
import '../app_state.dart';
import '../colors.dart';
import '../models/sensor_data.dart';

class UserHistoryScreen extends StatefulWidget {
  const UserHistoryScreen({super.key});

  @override
  State<UserHistoryScreen> createState() => _UserHistoryScreenState();
}

class _UserHistoryScreenState extends State<UserHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedTimeRange = '24h';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('History & Analytics'),
            backgroundColor: AppColors.cardBackground,
            elevation: 0,
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'TDS Levels'),
                Tab(text: 'Flow Rate'),
                Tab(text: 'System Logs'),
              ],
            ),
          ),
          body: Column(
            children: [
              // Time Range Selector
              _buildTimeRangeSelector(),

              // Tab Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildTDSChart(appState),
                    _buildFlowChart(appState),
                    _buildSystemLogs(appState),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTimeRangeSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        border: Border(
          bottom: BorderSide(color: AppColors.borderColor),
        ),
      ),
      child: Row(
        children: [
          const Text(
            'Time Range:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildTimeRangeButton('1h'),
                  _buildTimeRangeButton('6h'),
                  _buildTimeRangeButton('24h'),
                  _buildTimeRangeButton('7d'),
                  _buildTimeRangeButton('30d'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRangeButton(String range) {
    final isSelected = _selectedTimeRange == range;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedTimeRange = range;
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? AppColors.accent : Colors.grey.shade200,
          foregroundColor: isSelected ? Colors.white : Colors.black87,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 0,
        ),
        child: Text(range),
      ),
    );
  }

  Widget _buildTDSChart(AppState appState) {
    final data = _getFilteredData(appState.sensorHistory);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Card
          _buildSummaryCard(
            'TDS Levels',
            data.isNotEmpty ? data.last.tds : 0,
            'ppm',
            data.isNotEmpty ? data.last.tdsStatus : 'No Data',
            FontAwesomeIcons.droplet,
          ),
          const SizedBox(height: 24),

          // Chart
          _buildChartCard(
            'TDS Trend',
            data.isNotEmpty ? _buildTDSLineChart(data) : _buildNoDataWidget(),
          ),
          const SizedBox(height: 24),

          // Statistics
          _buildStatisticsCard('TDS Statistics', _calculateTDSStats(data)),
        ],
      ),
    );
  }

  Widget _buildFlowChart(AppState appState) {
    final data = _getFilteredData(appState.sensorHistory);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Card
          _buildSummaryCard(
            'Flow Rate',
            data.isNotEmpty ? data.last.flow : 0,
            'L/min',
            data.isNotEmpty && data.last.flow > 0 ? 'Active' : 'Idle',
            FontAwesomeIcons.water,
          ),
          const SizedBox(height: 24),

          // Chart
          _buildChartCard(
            'Flow Rate Trend',
            data.isNotEmpty ? _buildFlowLineChart(data) : _buildNoDataWidget(),
          ),
          const SizedBox(height: 24),

          // Statistics
          _buildStatisticsCard('Flow Statistics', _calculateFlowStats(data)),
        ],
      ),
    );
  }

  Widget _buildSystemLogs(AppState appState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Activity Card
          _buildRecentActivityCard(appState),
          const SizedBox(height: 24),

          // Device Status History
          _buildDeviceStatusCard(appState),
          const SizedBox(height: 24),

          // Notifications History
          _buildNotificationsCard(appState),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    double value,
    String unit,
    String status,
    IconData icon,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Icon(icon, color: AppColors.accent),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Padding(
                  padding: const EdgeInsets.only(bottom: 6),
                  child: Text(
                    unit,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: AppColors.accent.withValues(alpha: 0.1),
              ),
              child: Text(
                status,
                style: TextStyle(
                  fontSize: 12,
                  color: AppColors.accent,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartCard(String title, Widget chart) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: chart,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTDSLineChart(List<SensorData> data) {
    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.tds);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 50,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: (spots.length / 5).ceilToDouble(),
              getTitlesWidget: (value, meta) {
                if (value.toInt() < data.length) {
                  final timestamp = data[value.toInt()].timestamp;
                  return Text(
                    '${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toInt()}',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: AppColors.accent,
            barWidth: 3,
            isStrokeCapRound: true,
            belowBarData: BarAreaData(
              show: true,
              color: AppColors.accent.withValues(alpha: 0.1),
            ),
            dotData: const FlDotData(show: false),
          ),
        ],
      ),
    );
  }

  Widget _buildFlowLineChart(List<SensorData> data) {
    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.flow);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: (spots.length / 5).ceilToDouble(),
              getTitlesWidget: (value, meta) {
                if (value.toInt() < data.length) {
                  final timestamp = data[value.toInt()].timestamp;
                  return Text(
                    '${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}',
                    style: const TextStyle(fontSize: 10),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toStringAsFixed(1)}',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: Colors.blue,
            barWidth: 3,
            isStrokeCapRound: true,
            belowBarData: BarAreaData(
              show: true,
              color: Colors.blue.withValues(alpha: 0.1),
            ),
            dotData: const FlDotData(show: false),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FontAwesomeIcons.chartLine,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No data available',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Data will appear here once the device starts sending sensor readings',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard(String title, Map<String, String> stats) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            ...stats.entries.map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        entry.key,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        entry.value,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivityCard(AppState appState) {
    final recentData = appState.sensorHistory.take(10).toList();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            if (recentData.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'No recent activity',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ...recentData.map((data) => _buildActivityItem(data)),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(SensorData data) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: data.isRelayOn ? AppColors.accent : Colors.grey,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'TDS: ${data.tds.toStringAsFixed(1)}ppm, Flow: ${data.flow.toStringAsFixed(1)}L/min',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  _formatTimestamp(data.timestamp),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            data.isRelayOn ? FontAwesomeIcons.play : FontAwesomeIcons.pause,
            size: 12,
            color: data.isRelayOn ? AppColors.accent : Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceStatusCard(AppState appState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Device Status History',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatusHistoryItem(
              'MQTT Connection',
              appState.isMqttConnected ? 'Connected' : 'Disconnected',
              appState.isMqttConnected,
              DateTime.now(),
            ),
            _buildStatusHistoryItem(
              'Device Communication',
              appState.currentSensorData != null ? 'Active' : 'Inactive',
              appState.currentSensorData != null,
              appState.currentSensorData?.timestamp ?? DateTime.now(),
            ),
            _buildStatusHistoryItem(
              'Water Purifier',
              appState.relayState ? 'Running' : 'Stopped',
              appState.relayState,
              appState.currentSensorData?.timestamp ?? DateTime.now(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusHistoryItem(
      String title, String status, bool isGood, DateTime timestamp) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isGood ? AppColors.accent : AppColors.error,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$title: $status',
                  style: const TextStyle(fontSize: 14),
                ),
                Text(
                  _formatTimestamp(timestamp),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsCard(AppState appState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Notifications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            if (appState.notifications.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'No notifications',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ...appState.notifications
                  .take(5)
                  .map((notification) => _buildNotificationItem(notification)),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(String notification) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.bell,
            size: 12,
            color: AppColors.accent,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              notification,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  List<SensorData> _getFilteredData(List<SensorData> allData) {
    final now = DateTime.now();
    Duration duration;

    switch (_selectedTimeRange) {
      case '1h':
        duration = const Duration(hours: 1);
        break;
      case '6h':
        duration = const Duration(hours: 6);
        break;
      case '24h':
        duration = const Duration(hours: 24);
        break;
      case '7d':
        duration = const Duration(days: 7);
        break;
      case '30d':
        duration = const Duration(days: 30);
        break;
      default:
        duration = const Duration(hours: 24);
    }

    final cutoff = now.subtract(duration);
    return allData.where((data) => data.timestamp.isAfter(cutoff)).toList();
  }

  Map<String, String> _calculateTDSStats(List<SensorData> data) {
    if (data.isEmpty) {
      return {
        'Average': 'N/A',
        'Minimum': 'N/A',
        'Maximum': 'N/A',
        'Readings': '0',
      };
    }

    final tdsValues = data.map((d) => d.tds).toList();
    final average = tdsValues.reduce((a, b) => a + b) / tdsValues.length;
    final min = tdsValues.reduce((a, b) => a < b ? a : b);
    final max = tdsValues.reduce((a, b) => a > b ? a : b);

    return {
      'Average': '${average.toStringAsFixed(1)} ppm',
      'Minimum': '${min.toStringAsFixed(1)} ppm',
      'Maximum': '${max.toStringAsFixed(1)} ppm',
      'Readings': '${data.length}',
    };
  }

  Map<String, String> _calculateFlowStats(List<SensorData> data) {
    if (data.isEmpty) {
      return {
        'Average': 'N/A',
        'Total Volume': 'N/A',
        'Active Time': 'N/A',
        'Readings': '0',
      };
    }

    final flowValues = data.map((d) => d.flow).toList();
    final average = flowValues.reduce((a, b) => a + b) / flowValues.length;
    final totalVolume =
        flowValues.reduce((a, b) => a + b) / 60; // Convert to liters
    final activeReadings = data.where((d) => d.flow > 0).length;
    final activePercentage = (activeReadings / data.length * 100);

    return {
      'Average': '${average.toStringAsFixed(1)} L/min',
      'Total Volume': '${totalVolume.toStringAsFixed(1)} L',
      'Active Time': '${activePercentage.toStringAsFixed(1)}%',
      'Readings': '${data.length}',
    };
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
}
