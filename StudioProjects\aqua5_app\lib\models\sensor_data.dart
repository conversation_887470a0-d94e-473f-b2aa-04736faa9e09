import 'dart:convert';

/// Model class for sensor data received from ESP32 via MQTT
class SensorData {
  final double tds;
  final double flow;
  final String relay;
  final DateTime timestamp;
  final String deviceId;

  SensorData({
    required this.tds,
    required this.flow,
    required this.relay,
    required this.timestamp,
    required this.deviceId,
  });

  /// Create SensorData from JSON payload
  factory SensorData.fromJson(Map<String, dynamic> json, String deviceId) {
    return SensorData(
      tds: (json['tds'] as num).toDouble(),
      flow: (json['flow'] as num).toDouble(),
      relay: json['relay'] as String,
      timestamp: DateTime.now(),
      deviceId: deviceId,
    );
  }

  /// Create SensorData from MQTT payload string
  factory SensorData.fromMqttPayload(String payload, String deviceId) {
    try {
      final Map<String, dynamic> json = jsonDecode(payload);
      return SensorData.fromJson(json, deviceId);
    } catch (e) {
      throw FormatException('Invalid sensor data format: $e');
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'tds': tds,
      'flow': flow,
      'relay': relay,
      'timestamp': timestamp.toIso8601String(),
      'deviceId': deviceId,
    };
  }

  /// Get relay status as boolean
  bool get isRelayOn => relay.toUpperCase() == 'ON';

  /// Get TDS status based on level
  String get tdsStatus {
    if (tds < 50) return 'Excellent';
    if (tds < 150) return 'Good';
    if (tds < 300) return 'Fair';
    if (tds < 500) return 'Poor';
    return 'Unacceptable';
  }

  /// Get TDS status color
  String get tdsStatusColor {
    if (tds < 50) return 'green';
    if (tds < 150) return 'lightgreen';
    if (tds < 300) return 'yellow';
    if (tds < 500) return 'orange';
    return 'red';
  }

  @override
  String toString() {
    return 'SensorData(tds: $tds, flow: $flow, relay: $relay, deviceId: $deviceId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SensorData &&
        other.tds == tds &&
        other.flow == flow &&
        other.relay == relay &&
        other.deviceId == deviceId;
  }

  @override
  int get hashCode {
    return tds.hashCode ^
        flow.hashCode ^
        relay.hashCode ^
        deviceId.hashCode;
  }
}

/// Model for device information
class DeviceInfo {
  final String id;
  final String name;
  final bool isConnected;
  final DateTime lastSeen;
  final SensorData? lastData;

  DeviceInfo({
    required this.id,
    required this.name,
    required this.isConnected,
    required this.lastSeen,
    this.lastData,
  });

  DeviceInfo copyWith({
    String? id,
    String? name,
    bool? isConnected,
    DateTime? lastSeen,
    SensorData? lastData,
  }) {
    return DeviceInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      isConnected: isConnected ?? this.isConnected,
      lastSeen: lastSeen ?? this.lastSeen,
      lastData: lastData ?? this.lastData,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'isConnected': isConnected,
      'lastSeen': lastSeen.toIso8601String(),
      'lastData': lastData?.toJson(),
    };
  }

  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      isConnected: json['isConnected'] as bool,
      lastSeen: DateTime.parse(json['lastSeen'] as String),
      lastData: json['lastData'] != null 
          ? SensorData.fromJson(json['lastData'], json['id'])
          : null,
    );
  }
}

/// Model for system logs
class SystemLog {
  final String id;
  final String message;
  final String level; // info, warning, error
  final DateTime timestamp;
  final String? userId;
  final String? deviceId;
  final Map<String, dynamic>? metadata;

  SystemLog({
    required this.id,
    required this.message,
    required this.level,
    required this.timestamp,
    this.userId,
    this.deviceId,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'message': message,
      'level': level,
      'timestamp': timestamp.toIso8601String(),
      'userId': userId,
      'deviceId': deviceId,
      'metadata': metadata,
    };
  }

  factory SystemLog.fromJson(Map<String, dynamic> json) {
    return SystemLog(
      id: json['id'] as String,
      message: json['message'] as String,
      level: json['level'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userId: json['userId'] as String?,
      deviceId: json['deviceId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }
}
