import 'dart:async';
import 'dart:convert';
import 'package:aqua5_app/constance.dart';
import 'package:aqua5_app/models/sensor_data.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

enum MqttConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error
}

class MQTTService {
  late MqttServerClient client;
  final String clientId;

  // Connection state
  MqttConnectionState _connectionState = MqttConnectionState.disconnected;
  MqttConnectionState get connectionState => _connectionState;

  // Stream controllers for real-time updates
  final StreamController<SensorData> _sensorDataController =
      StreamController<SensorData>.broadcast();
  final StreamController<MqttConnectionState> _connectionStateController =
      StreamController<MqttConnectionState>.broadcast();
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // Streams
  Stream<SensorData> get sensorDataStream => _sensorDataController.stream;
  Stream<MqttConnectionState> get connectionStateStream =>
      _connectionStateController.stream;
  Stream<String> get errorStream => _errorController.stream;

  // Callbacks for backward compatibility
  Function(String, String)? onMessage;
  Function(SensorData)? onSensorData;
  Function(MqttConnectionState)? onConnectionStateChanged;
  Function(String)? onError;

  // Auto-reconnect settings
  bool _autoReconnect = true;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 5;
  static const Duration reconnectDelay = Duration(seconds: 5);

  MQTTService(this.clientId) {
    _initializeClient();
  }

  void _initializeClient() {
    client = MqttServerClient(AppConstants.mqttBroker, clientId);
    client.port = AppConstants.mqttPort;
    client.logging(on: false); // Disable verbose logging
    client.keepAlivePeriod = 30;
    client.autoReconnect = false; // We handle reconnection manually
    client.onDisconnected = _onDisconnected;
    client.onConnected = _onConnected;
    client.onSubscribed = _onSubscribed;
    client.onAutoReconnect = _onAutoReconnect;
  }

  Future<bool> connect() async {
    if (_connectionState == MqttConnectionState.connected) {
      return true;
    }

    _updateConnectionState(MqttConnectionState.connecting);

    try {
      print(
          '🔌 Connecting to MQTT broker: ${AppConstants.mqttBroker}:${AppConstants.mqttPort}');
      await client.connect();
      return _connectionState == MqttConnectionState.connected;
    } catch (e) {
      print('❌ MQTT Connection failed: $e');
      _handleError('Connection failed: $e');
      _updateConnectionState(MqttConnectionState.error);

      if (_autoReconnect && _reconnectAttempts < maxReconnectAttempts) {
        _scheduleReconnect();
      }

      return false;
    }
  }

  void _onConnected() {
    print('✅ MQTT Connected successfully');
    _reconnectAttempts = 0;
    _updateConnectionState(MqttConnectionState.connected);

    // Subscribe to sensor status topic
    _subscribeToTopics();

    // Set up message listener
    client.updates!.listen((List<MqttReceivedMessage<MqttMessage>> c) {
      final message = c[0].payload as MqttPublishMessage;
      final payload =
          MqttPublishPayload.bytesToStringAsString(message.payload.message);
      final topic = c[0].topic;

      _handleIncomingMessage(topic, payload);
    });
  }

  void _onDisconnected() {
    print('🔌 MQTT Disconnected');
    _updateConnectionState(MqttConnectionState.disconnected);

    if (_autoReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  void _onSubscribed(String topic) {
    print('📡 Subscribed to topic: $topic');
  }

  void _onAutoReconnect() {
    print('🔄 MQTT Auto-reconnecting...');
  }

  void _subscribeToTopics() {
    // Subscribe to device status topics (supports multiple devices)
    client.subscribe('device/+/status', MqttQos.atLeastOnce);
    client.subscribe(AppConstants.mqttTopicStatus, MqttQos.atLeastOnce);

    // Subscribe to device announcements
    client.subscribe('device/+/announce', MqttQos.atLeastOnce);
  }

  void _handleIncomingMessage(String topic, String payload) {
    try {
      // Call legacy callback for backward compatibility
      onMessage?.call(topic, payload);

      // Handle sensor data
      if (topic.contains('status') || topic == AppConstants.mqttTopicStatus) {
        _handleSensorData(topic, payload);
      }

      // Handle device announcements
      if (topic.contains('announce')) {
        _handleDeviceAnnouncement(topic, payload);
      }
    } catch (e) {
      print('❌ Error handling message: $e');
      _handleError('Message handling error: $e');
    }
  }

  void _handleSensorData(String topic, String payload) {
    try {
      // Extract device ID from topic (device/DEVICE_ID/status)
      String deviceId = AppConstants.defaultDeviceId;
      if (topic.contains('device/')) {
        final parts = topic.split('/');
        if (parts.length >= 2) {
          deviceId = parts[1];
        }
      }

      final sensorData = SensorData.fromMqttPayload(payload, deviceId);

      // Emit to stream
      _sensorDataController.add(sensorData);

      // Call callback
      onSensorData?.call(sensorData);

      print(
          '📊 Sensor data received: TDS=${sensorData.tds}ppm, Flow=${sensorData.flow}L/min, Relay=${sensorData.relay}');
    } catch (e) {
      print('❌ Error parsing sensor data: $e');
      _handleError('Sensor data parsing error: $e');
    }
  }

  void _handleDeviceAnnouncement(String topic, String payload) {
    print('📢 Device announcement: $topic -> $payload');
  }

  void _updateConnectionState(MqttConnectionState state) {
    _connectionState = state;
    _connectionStateController.add(state);
    onConnectionStateChanged?.call(state);
  }

  void _handleError(String error) {
    _errorController.add(error);
    onError?.call(error);
  }

  void _scheduleReconnect() {
    _reconnectAttempts++;
    print(
        '🔄 Scheduling reconnect attempt $_reconnectAttempts/$maxReconnectAttempts in ${reconnectDelay.inSeconds}s');

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () {
      if (_connectionState != MqttConnectionState.connected) {
        connect();
      }
    });
  }

  /// Publish a message to a topic
  bool publish(String topic, String message) {
    if (_connectionState != MqttConnectionState.connected) {
      print('❌ Cannot publish: MQTT not connected');
      return false;
    }

    try {
      final builder = MqttClientPayloadBuilder();
      builder.addString(message);
      client.publishMessage(topic, MqttQos.atLeastOnce, builder.payload!);
      print('📤 Published to $topic: $message');
      return true;
    } catch (e) {
      print('❌ Publish failed: $e');
      _handleError('Publish failed: $e');
      return false;
    }
  }

  /// Control relay state
  bool controlRelay(String deviceId, bool isOn) {
    final command =
        isOn ? AppConstants.relayOnCommand : AppConstants.relayOffCommand;
    final topic = 'device/$deviceId/control';
    return publish(topic, command);
  }

  /// Subscribe to a custom topic
  void subscribe(String topic, {MqttQos qos = MqttQos.atLeastOnce}) {
    if (_connectionState == MqttConnectionState.connected) {
      client.subscribe(topic, qos);
    }
  }

  /// Unsubscribe from a topic
  void unsubscribe(String topic) {
    if (_connectionState == MqttConnectionState.connected) {
      client.unsubscribe(topic);
    }
  }

  /// Disconnect from MQTT broker
  void disconnect() {
    _updateConnectionState(MqttConnectionState.disconnecting);
    _reconnectTimer?.cancel();
    _autoReconnect = false;
    client.disconnect();
  }

  /// Dispose resources
  void dispose() {
    _reconnectTimer?.cancel();
    _sensorDataController.close();
    _connectionStateController.close();
    _errorController.close();
    disconnect();
  }

  /// Enable/disable auto-reconnect
  void setAutoReconnect(bool enabled) {
    _autoReconnect = enabled;
  }

  /// Reset reconnection attempts
  void resetReconnectAttempts() {
    _reconnectAttempts = 0;
  }

  /// Get connection status as string
  String get connectionStatusString {
    switch (_connectionState) {
      case MqttConnectionState.disconnected:
        return 'Disconnected';
      case MqttConnectionState.connecting:
        return 'Connecting...';
      case MqttConnectionState.connected:
        return 'Connected';
      case MqttConnectionState.disconnecting:
        return 'Disconnecting...';
      case MqttConnectionState.error:
        return 'Error';
    }
  }

  /// Check if connected
  bool get isConnected => _connectionState == MqttConnectionState.connected;
}
