import 'package:aqua5_app/screens/widgets/custom_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../app_state.dart';
import '../colors.dart';
import '../constance.dart';

class UserControlScreen extends StatefulWidget {
  const UserControlScreen({super.key});

  @override
  State<UserControlScreen> createState() => _UserControlScreenState();
}

class _UserControlScreenState extends State<UserControlScreen> {
  bool _isControlling = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Device Control'),
            backgroundColor: AppColors.cardBackground,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(18.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Connection Status Card
                _buildConnectionStatusCard(appState),
                const SizedBox(height: 24),

                // Real-time Data Card
                _buildRealTimeDataCard(appState),
                const SizedBox(height: 24),

                // Relay Control Card
                _buildRelayControlCard(appState),
                const SizedBox(height: 24),

                // Quick Actions Card
                _buildQuickActionsCard(appState),
                const SizedBox(height: 24),

                // System Status Card
                _buildSystemStatusCard(appState),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildConnectionStatusCard(AppState appState) {
    final isConnected = appState.isMqttConnected;
    final statusColor = isConnected ? AppColors.accent : AppColors.error;
    final statusIcon =
        isConnected ? CupertinoIcons.wifi : CupertinoIcons.wifi_slash;
    final statusText = appState.connectionStatusString;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Connection Status',
                  style: TextStyle(
                    fontSize: 18,
                    color: Color.fromARGB(255, 0, 0, 0),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: statusColor.withOpacity(0.1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 6),
                      Text(
                        statusText,
                        style: TextStyle(fontSize: 12, color: statusColor),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isConnected
                  ? 'Device is connected and receiving real-time data'
                  : 'Device is disconnected. Check your internet connection.',
              style: const TextStyle(
                fontSize: 14,
                color: Color.fromARGB(180, 0, 0, 0),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRealTimeDataCard(AppState appState) {
    final sensorData = appState.currentSensorData;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Real-time Data',
                  style: TextStyle(
                    fontSize: 18,
                    color: Color.fromARGB(255, 0, 0, 0),
                    fontWeight: FontWeight.w400,
                  ),
                ),
                if (sensorData != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: AppColors.accent.withOpacity(0.1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(FontAwesomeIcons.arrowsRotate,
                            size: 12, color: AppColors.accent),
                        const SizedBox(width: 6),
                        Text(
                          'Live',
                          style:
                              TextStyle(fontSize: 12, color: AppColors.accent),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            if (sensorData != null) ...[
              // TDS Level
              Row(
                children: [
                  Expanded(
                    child: _buildDataItem(
                      'TDS Level',
                      '${sensorData.tds.toStringAsFixed(1)} ppm',
                      sensorData.tdsStatus,
                      FontAwesomeIcons.droplet,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDataItem(
                      'Flow Rate',
                      '${sensorData.flow.toStringAsFixed(1)} L/min',
                      sensorData.flow > 0 ? 'Active' : 'Idle',
                      FontAwesomeIcons.water,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                'Last updated: ${_formatTimestamp(sensorData.timestamp)}',
                style: const TextStyle(
                  fontSize: 12,
                  color: Color.fromARGB(150, 0, 0, 0),
                ),
              ),
            ] else ...[
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text(
                    'No sensor data available.\nCheck device connection.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Color.fromARGB(150, 0, 0, 0),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDataItem(
      String label, String value, String status, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: AppColors.accent),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Color.fromARGB(150, 0, 0, 0),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Color.fromARGB(255, 0, 0, 0),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          status,
          style: TextStyle(
            fontSize: 12,
            color: AppColors.accent,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildRelayControlCard(AppState appState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Water Purifier Control',
              style: TextStyle(
                fontSize: 18,
                color: Color.fromARGB(255, 0, 0, 0),
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appState.relayState ? 'Purifier ON' : 'Purifier OFF',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color.fromARGB(255, 0, 0, 0),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      appState.relayState
                          ? 'Water purification is active'
                          : 'Water purification is stopped',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color.fromARGB(150, 0, 0, 0),
                      ),
                    ),
                  ],
                ),
                CustomSwitch(
                  isOn: appState.relayState,
                  isLoading: _isControlling,
                  onChanged: (value) => _handleRelayControl(appState, value),
                ),
              ],
            ),
            if (!appState.isMqttConnected) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.warning.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.warning.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(FontAwesomeIcons.triangleExclamation,
                        size: 16, color: AppColors.warning),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Device not connected. Control unavailable.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(AppState appState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                color: Color.fromARGB(255, 0, 0, 0),
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Emergency Stop',
                    FontAwesomeIcons.stop,
                    AppColors.error,
                    () => _handleEmergencyStop(appState),
                    enabled: appState.isMqttConnected && appState.relayState,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    'Quick Start',
                    FontAwesomeIcons.play,
                    AppColors.accent,
                    () => _handleQuickStart(appState),
                    enabled: appState.isMqttConnected && !appState.relayState,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
      String label, IconData icon, Color color, VoidCallback onPressed,
      {bool enabled = true}) {
    return ElevatedButton(
      onPressed: enabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: enabled ? color : Colors.grey.shade300,
        foregroundColor: enabled ? Colors.white : Colors.grey.shade600,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatusCard(AppState appState) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Status',
              style: TextStyle(
                fontSize: 18,
                color: Color.fromARGB(255, 0, 0, 0),
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatusItem(
              'MQTT Connection',
              appState.isMqttConnected ? 'Connected' : 'Disconnected',
              appState.isMqttConnected,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Device Status',
              appState.currentSensorData != null ? 'Online' : 'Offline',
              appState.currentSensorData != null,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Data Stream',
              appState.sensorHistory.isNotEmpty ? 'Active' : 'No Data',
              appState.sensorHistory.isNotEmpty,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String status, bool isGood) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Color.fromARGB(150, 0, 0, 0),
          ),
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isGood ? AppColors.accent : AppColors.error,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              status,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isGood ? AppColors.accent : AppColors.error,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Control methods
  Future<void> _handleRelayControl(AppState appState, bool value) async {
    if (!appState.isMqttConnected) {
      _showErrorSnackBar('Device not connected');
      return;
    }

    setState(() {
      _isControlling = true;
    });

    try {
      final success =
          appState.controlRelay(AppConstants.defaultDeviceId, value);
      if (success) {
        _showSuccessSnackBar(value
            ? 'Purifier started successfully'
            : 'Purifier stopped successfully');
      } else {
        _showErrorSnackBar('Failed to control device');
      }
    } catch (e) {
      _showErrorSnackBar('Error: $e');
    } finally {
      setState(() {
        _isControlling = false;
      });
    }
  }

  void _handleEmergencyStop(AppState appState) {
    _handleRelayControl(appState, false);
  }

  void _handleQuickStart(AppState appState) {
    _handleRelayControl(appState, true);
  }

  // Helper methods
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.accent,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
